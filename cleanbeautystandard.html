<p>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" /><link
    rel="preconnect"
    href="https://fonts.googleapis.com"
  />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="" />
  <link
    href="https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500;600;700&amp;display=swap"
    rel="stylesheet"
  />
  <link
    href="https://fonts.googleapis.com/css2?family=Old+Standard+TT:ital,wght@0,400;0,700;1,400&amp;display=swap"
    rel="stylesheet"
  />
</p>
<style>
  .catham-magazine-article {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  .catham-magazine-article * {
    box-sizing: border-box;
  }

  .catham-magazine-wrapper {
    font-family: "Fira Code", monospace;
    font-weight: 400;
    line-height: 1.6;
    color: #1a1a1a;
    background: #f5f5f5;
    font-size: 12px;
    padding: 40px 20px;
    margin: 0;
    isolation: isolate;
    min-height: 100vh;
  }

  .catham-magazine-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0;
    background-color: #ffffff;
    position: relative;
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
  }

  /* Magazine Layout Structure */
  .magazine-spread {
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 100vh;
  }

  .magazine-page {
    position: relative;
    padding: 60px 40px;
    display: flex;
    flex-direction: column;
  }

  .magazine-page.left-page {
    background: #ffffff;
    color: #1a1a1a;
    position: relative;
  }

  /* Magazine Center Separator */
  .magazine-spread {
    position: relative;
  }

  .magazine-spread::after {
    content: "";
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(
      180deg,
      transparent 0%,
      #d4d4d4 10%,
      #999999 50%,
      #d4d4d4 90%,
      transparent 100%
    );
    transform: translateX(-50%);
    z-index: 10;
  }

  .magazine-spread::before {
    content: "";
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: #ffffff;
    border: 2px solid #999999;
    border-radius: 50%;
    z-index: 11;
  }

  .magazine-page.right-page {
    background: #ffffff;
    color: #1a1a1a;
    border-left: 1px solid #e8e8e8;
  }

  /* Magazine Header */
  .catham-magazine-header {
    grid-column: 1 / -1;
    text-align: center;
    padding: 80px 60px 60px;
    background: #ffffff;
    border-bottom: 1px solid #e8e8e8;
    position: relative;
  }

  .catham-magazine-header::before {
    content: "CATHÁM EDIT";
    position: absolute;
    top: 20px;
    left: 60px;
    font-family: "Fira Code", monospace;
    font-size: 10px;
    font-weight: 600;
    letter-spacing: 2px;
    color: #999;
    text-transform: uppercase;
  }

  .catham-magazine-header::after {
    content: "01";
    position: absolute;
    top: 20px;
    right: 60px;
    font-family: "Fira Code", monospace;
    font-size: 10px;
    font-weight: 600;
    color: #999;
  }

  .catham-main-title {
    font-family: "Old Standard TT", serif;
    font-weight: 700;
    font-size: 3.5rem;
    color: #1a1a1a;
    letter-spacing: -0.03em;
    margin-bottom: 20px;
    line-height: 0.9;
    text-transform: uppercase;
    position: relative;
  }

  .catham-subtitle {
    font-family: "Old Standard TT", serif;
    font-weight: 400;
    font-style: italic;
    font-size: 1.2rem;
    color: #666666;
    letter-spacing: 0.08em;
    text-transform: lowercase;
    margin-top: 15px;
  }

  /* Magazine Column Layout */
  .magazine-columns {
    columns: 2;
    column-gap: 40px;
    column-rule: 1px solid #e8e8e8;
    text-align: justify;
    hyphens: auto;
  }

  .magazine-columns.dark {
    column-rule-color: #333333;
  }

  .magazine-column-break {
    break-after: column;
  }

  /* Magazine Typography */
  .magazine-headline {
    font-family: "Old Standard TT", serif;
    font-weight: 700;
    font-size: 2.2rem;
    line-height: 1.1;
    margin-bottom: 20px;
    letter-spacing: -0.02em;
  }

  .left-page .magazine-headline {
    color: #1a1a1a;
  }

  .right-page .magazine-headline {
    color: #1a1a1a;
  }

  .magazine-subhead {
    font-family: "Fira Code", monospace;
    font-size: 10px;
    font-weight: 600;
    letter-spacing: 2px;
    text-transform: uppercase;
    margin-bottom: 30px;
    opacity: 0.7;
  }

  .magazine-body {
    font-size: 12px;
    line-height: 1.7;
    margin-bottom: 20px;
    text-align: justify;
    hyphens: auto;
  }

  .left-page .magazine-body {
    color: #333333;
  }

  .right-page .magazine-body {
    color: #333333;
  }

  .magazine-intro {
    font-size: 14px;
    line-height: 1.8;
    margin-bottom: 30px;
    font-weight: 400;
  }

  .magazine-intro:first-letter {
    font-family: "Old Standard TT", serif;
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1;
    float: left;
    margin: 5px 8px 0 0;
  }

  .left-page .magazine-intro:first-letter {
    color: #1a1a1a;
  }

  .right-page .magazine-intro:first-letter {
    color: #1a1a1a;
  }

  /* Magazine Visual Elements */
  .magazine-pullquote {
    font-family: "Old Standard TT", serif;
    font-style: italic;
    font-size: 1.4rem;
    line-height: 1.4;
    margin: 40px 0;
    padding: 30px 0;
    text-align: center;
    border-top: 2px solid;
    border-bottom: 2px solid;
    break-inside: avoid;
  }

  .left-page .magazine-pullquote {
    border-color: #1a1a1a;
    color: #1a1a1a;
  }

  .right-page .magazine-pullquote {
    border-color: #1a1a1a;
    color: #1a1a1a;
  }

  .magazine-sidebar {
    background: rgba(0, 0, 0, 0.05);
    padding: 25px;
    margin: 30px 0;
    border-left: 4px solid;
    break-inside: avoid;
  }

  .left-page .magazine-sidebar {
    background: rgba(0, 0, 0, 0.05);
    border-left-color: #1a1a1a;
  }

  .right-page .magazine-sidebar {
    background: rgba(0, 0, 0, 0.05);
    border-left-color: #1a1a1a;
  }

  .standards-list {
    padding: 40px;
    border-radius: 0;
    margin: 40px 0;
    border-left: 4px solid;
  }

  .white-section .standards-list {
    background-color: #f9f9f9;
    border-left-color: #1a1a1a;
    border: 1px solid #e8e8e8;
  }

  .black-section .standards-list {
    background-color: #111111;
    border-left-color: #ffffff;
    border: 1px solid #333333;
  }

  .standards-item {
    margin-bottom: 25px;
    padding-left: 20px;
    position: relative;
  }

  .standards-item::before {
    content: "✓";
    position: absolute;
    left: 0;
    font-weight: 700;
    font-size: 1.2rem;
  }

  .white-section .standards-item::before {
    color: #1a1a1a;
  }

  .black-section .standards-item::before {
    color: #ffffff;
  }

  .standards-item-title {
    font-weight: 600;
    margin-bottom: 8px;
  }

  .white-section .standards-item-title {
    color: #1a1a1a;
  }

  .black-section .standards-item-title {
    color: #ffffff;
  }

  .blacklist {
    padding: 40px;
    border-radius: 0;
    margin: 40px 0;
  }

  .white-section .blacklist {
    background-color: #f9f9f9;
    border: 1px solid #e8e8e8;
  }

  .black-section .blacklist {
    background-color: #111111;
    border: 1px solid #333333;
  }

  .blacklist-title {
    font-family: "Old Standard TT", serif;
    font-weight: 700;
    font-size: 1.5rem;
    margin-bottom: 30px;
    text-align: center;
  }

  .white-section .blacklist-title {
    color: #1a1a1a;
  }

  .black-section .blacklist-title {
    color: #ffffff;
  }

  .blacklist-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
  }

  .blacklist-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
  }

  .white-section .blacklist-item {
    color: #666666;
  }

  .black-section .blacklist-item {
    color: #cccccc;
  }

  .blacklist-item::before {
    content: "❌";
    margin-right: 12px;
    font-size: 1.1rem;
  }

  .quote {
    font-family: "Old Standard TT", serif;
    font-style: italic;
    font-size: 1.3rem;
    text-align: center;
    margin: 0;
    padding: 80px 60px;
    line-height: 1.6;
    background-color: #000000;
    color: #ffffff;
  }

  .closing {
    text-align: center;
    padding: 80px 60px;
    background: #ffffff;
    color: #1a1a1a;
  }

  .closing-text {
    font-family: "Old Standard TT", serif;
    font-weight: 700;
    font-size: 1.4rem;
    letter-spacing: 0.02em;
  }

  /* Ecocert Certification Section - Magazine Style */
  .ecocert-section {
    background: #000000;
    color: #ffffff;
    padding: 80px 60px;
    margin: 0;
    position: relative;
  }

  .ecocert-container {
    max-width: 100%;
    margin: 0 auto;
  }

  .ecocert-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 80px;
    align-items: start;
  }

  .ecocert-text {
    padding-right: 40px;
  }

  .ecocert-visual {
    display: flex;
    flex-direction: column;
    gap: 30px;
    align-items: flex-start;
    position: relative;
  }

  .ecocert-logo {
    background: #ffffff;
    padding: 15px;
    border: 3px solid #ffffff;
    transition: all 0.3s ease;
    display: inline-block;
    position: relative;
  }

  .ecocert-logo:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
  }

  .ecocert-logo img {
    width: 50px;
    height: auto;
    display: block;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  .ecocert-image {
    overflow: hidden;
    border: 3px solid #ffffff;
    transition: all 0.3s ease;
    position: relative;
  }

  .ecocert-image::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
  }

  .ecocert-image:hover::after {
    opacity: 1;
  }

  .ecocert-image img {
    width: 100%;
    height: auto;
    display: block;
    filter: grayscale(30%);
    transition: filter 0.3s ease;
  }

  .ecocert-image:hover img {
    filter: grayscale(0%);
  }

  @media (max-width: 768px) {
    .catham-magazine-wrapper {
      padding: 20px 10px;
    }

    .catham-magazine-container {
      border-radius: 0;
    }

    .catham-main-title {
      font-size: 2.5rem;
    }

    .magazine-spread {
      grid-template-columns: 1fr;
    }

    .magazine-page {
      padding: 40px 30px;
      min-height: auto;
    }

    .magazine-page.left-page {
      border-right: none;
      border-bottom: 1px solid #e8e8e8;
    }

    .magazine-columns {
      columns: 1;
      column-gap: 0;
      column-rule: none;
    }

    .magazine-columns.dark {
      column-rule: none;
    }

    .magazine-column-break {
      break-after: auto;
    }

    .magazine-headline {
      font-size: 1.8rem;
    }

    .catham-magazine-header::before,
    .catham-magazine-header::after {
      display: none;
    }
  }

  @media (max-width: 480px) {
    .catham-main-title {
      font-size: 2rem;
    }

    .catham-section-title {
      font-size: 1.4rem;
    }

    .catham-magazine-wrapper {
      font-size: 12px;
    }
  }
</style>
<div class="catham-magazine-wrapper">
  <div class="catham-magazine-container">
    <header class="catham-magazine-header">
      <h1 class="catham-main-title">Cathám Clean Beauty Standard</h1>
      <p class="catham-subtitle">crafting beauty with intention</p>
    </header>

    <div class="magazine-spread">
      <!-- Left Page -->
      <div class="magazine-page left-page">
        <h2 class="magazine-subhead">Our Philosophy</h2>

        <p class="magazine-intro">
          We see skincare as more than routine—it's a quiet ritual of
          self-respect. That's why every Cathám formulation is rooted in clean
          beauty principles: pure ingredients and a deep respect for both your
          skin and the Earth.
        </p>

        <p class="magazine-body">
          <strong>Clean isn't a claim—it's our commitment.</strong>
          The EU restricts 1,600+ cosmetic ingredients. The U.S.? Barely 20. We
          choose the stricter path, not for the headline—but because integrity
          should be visible on your skin.
        </p>

        <h3 class="magazine-headline">We don't take shortcuts.</h3>
        <p class="magazine-body">
          We never compromise on ingredient quality, product integrity, or
          sustainability. You'll never find vague blends or hidden additives in
          our line—just full transparency and formulas you can trust.
        </p>

        <div class="magazine-sidebar">
          <h4
            style="
              font-family: 'Old Standard TT', serif;
              font-weight: 700;
              margin-bottom: 20px;
              font-size: 1.1rem;
              color: #1a1a1a;
            "
          >
            What you'll never find in our products:
          </h4>
          <div
            style="
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 8px 15px;
              font-size: 11px;
              line-height: 1.4;
            "
          >
            <div>• Carcinogens</div>
            <div>• Parabens</div>
            <div>• Phthalates</div>
            <div>• SLS Sulfates</div>
            <div>• Benzene & Derivatives</div>
            <div>• Silicones & Siloxanes</div>
            <div>• Formaldehyde, BHA</div>
            <div>• Synthetic dyes</div>
            <div>• Coal tar, carbon black</div>
            <div>• Synthetic fragrances</div>
          </div>
        </div>

        <p class="magazine-body" style="margin-top: 30px; font-style: italic">
          Every product is developed under the Cathám Clean Beauty Standard—a
          strict, science-backed blacklist of ingredients we refuse to use, not
          because it's easy, but because it matters.
        </p>
      </div>
      <!-- Right Page -->
      <div class="magazine-page right-page">
        <h2 class="magazine-subhead">Certified Excellence</h2>

        <div
          style="
            display: flex;
            align-items: flex-start;
            gap: 30px;
            margin-bottom: 40px;
          "
        >
          <div style="flex: 1">
            <h3 class="magazine-headline">Ecocert Certified</h3>
            <p class="magazine-body">
              Every Cathám product is certified by Ecocert, the world's leading
              organic certification body. This prestigious certification ensures
              our formulations meet the highest standards for natural and
              organic cosmetics.
            </p>
            <p class="magazine-body">
              Ecocert's rigorous standards verify that our ingredients are
              sourced ethically, our production processes are environmentally
              conscious, and our commitment to clean beauty is genuine—not just
              marketing.
            </p>
          </div>
          <div style="flex-shrink: 0">
            <div
              style="
                background: #f8f8f8;
                padding: 15px;
                border: 2px solid #e8e8e8;
              "
            >
              <img
                alt="Ecocert Cosmos Certification"
                src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/ecocert_cosmos.webp?v=1744198746"
                style="width: 60px; height: auto; display: block"
              />
            </div>
          </div>
        </div>

        <!-- Product Image -->
        <div style="margin: 40px 0; text-align: center">
          <img
            alt="Cathám Clean Beauty Products"
            src="https://cdn.shopify.com/s/files/1/0909/6560/6735/files/f34b0eda-250c-448c-9e48-688d048c2056.jpg?v=1745603110"
            style="
              width: 100%;
              max-width: 300px;
              height: auto;
              border: 2px solid #e8e8e8;
              filter: grayscale(10%);
            "
          />
        </div>

        <h3 class="magazine-headline">What Cathám stands for:</h3>

        <div
          style="
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin: 30px 0;
          "
        >
          <div style="padding-bottom: 15px; border-bottom: 1px solid #e8e8e8">
            <strong style="color: #1a1a1a; font-size: 13px"
              >100% Cruelty-Free</strong
            ><br />
            <span style="font-size: 11px; color: #666666"
              >No animal testing. Not now, not ever.</span
            >
          </div>
          <div style="padding-bottom: 15px; border-bottom: 1px solid #e8e8e8">
            <strong style="color: #1a1a1a; font-size: 13px"
              >Vegan-Friendly</strong
            ><br />
            <span style="font-size: 11px; color: #666666"
              >All our formulas are plant-based.</span
            >
          </div>
          <div style="padding-bottom: 15px; border-bottom: 1px solid #e8e8e8">
            <strong style="color: #1a1a1a; font-size: 13px"
              >Conscious Formulation</strong
            ><br />
            <span style="font-size: 11px; color: #666666"
              >Every step designed to reduce harm and preserve ecosystems.</span
            >
          </div>
          <div style="padding-bottom: 15px; border-bottom: 1px solid #e8e8e8">
            <strong style="color: #1a1a1a; font-size: 13px"
              >Sustainable Sourcing</strong
            ><br />
            <span style="font-size: 11px; color: #666666"
              >We use natural resources with care—never exploiting, always
              preserving.</span
            >
          </div>
          <div style="padding-bottom: 15px">
            <strong style="color: #1a1a1a; font-size: 13px"
              >Full Transparency</strong
            ><br />
            <span style="font-size: 11px; color: #666666"
              >Complete ingredient lists clearly stated. No "secret
              formulas."</span
            >
          </div>
        </div>

        <div class="magazine-pullquote">
          "We don't just formulate for skin—we formulate for the future."
        </div>
      </div>
    </div>

    <footer
      style="
        grid-column: 1 / -1;
        text-align: center;
        padding: 40px;
        background: #ffffff;
        border-top: 1px solid #e8e8e8;
        position: relative;
      "
    >
      <div
        style="
          font-family: 'Old Standard TT', serif;
          font-weight: 700;
          font-size: 1.2rem;
          color: #1a1a1a;
          letter-spacing: 0.1em;
        "
      >
        CATHÁM
      </div>
      <div
        style="
          position: absolute;
          bottom: 20px;
          left: 60px;
          font-family: 'Fira Code', monospace;
          font-size: 10px;
          color: #999;
        "
      >
        CLEAN BEAUTY STANDARD
      </div>
      <div
        style="
          position: absolute;
          bottom: 20px;
          right: 60px;
          font-family: 'Fira Code', monospace;
          font-size: 10px;
          color: #999;
        "
      >
        02
      </div>
    </footer>
  </div>
</div>
